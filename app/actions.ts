"use server";

import { encodedRedirect } from "@/utils/utils";
import { createClient } from "@/utils/supabase/server";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

export const signUpAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const password = formData.get("password")?.toString();
  const supabase = await createClient();
  const origin = (await headers()).get("origin");

  if (!email || !password) {
    return {
      success: false,
      message: {"error" : "Email and password are required"}
    }
  }

  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${origin}/auth/callback?signup=true`,
    },
  });

  if (error) {
    return {
      success: false,
      message: {"error" : error.message}
    }
  }

  //
  if (data.user) {
    try {
      const { data: existingUser, error: userError } = await supabase
        .from('users')
        .select('id')
        .eq('id', data.user.id)
        .single();

      if (userError && userError.code !== 'PGRST116') {
        console.error('Error checking user existence:', userError);
      }

      // if (!existingUser) {
      //   let userEmail = '';
      //   try {
      //     if (data.user.email) {
      //       userEmail = data.user.email;
      //     }
      //   } catch (error) {
      //     console.error('Error accessing email:', error);
      //   }

      //   const { error: insertError } = await supabase
      //     .from('users')
      //     .insert({
      //       id: data.user.id,
      //       email: userEmail,
      //       full_name: '',
      //       created_at: new Date().toISOString(),
      //       updated_at: new Date().toISOString(),
      //       is_active: true
      //     });

      //   if (insertError) {
      //     console.error('Error inserting user:', insertError);
      //   }
      // }
    } catch (error) {
      console.error('Error in user creation process:', error);
    }
  }

  return {
    success: true,
    message: {"success" : "Thanks for signing up! Please check your email for a verification link."}
  };

};

export const signInAction = async (formData: FormData) => {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const userRegion = formData.get("userRegion") as string;
  const supabase = await createClient();

  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    return {
      success: false,
      message: {"error" : error.message}
    }
  }

  if (data.user) {
    try {
      const { data: existingUser, error: userError } = await supabase
        .from('users')
        .select('id')
        .eq('id', data.user.id)
        .single();

      if (userError && userError.code !== 'PGRST116') {
        console.error('Error checking user existence:', userError);
      }

      if (!existingUser) {
        return {
          success: false,
          message: {"error" :"User not found"}
        };
      }

      // Update the last_login_at field
      const currentTime = new Date().toISOString();
      const { error: updateError } = await supabase
        .from('users')
        .update({
          last_login_at: currentTime,
          updated_at: currentTime
        })
        .eq('id', data.user.id);

      if (updateError) {
        console.error('Error updating last login time:', updateError);
        // Continue with login even if updating the timestamp fails
      } else {
        console.log('Updated last_login_at for user:', data.user.id);
      }

      // Store region in session user metadata only
      if (userRegion) {
        const { error: sessionUpdateError } = await supabase.auth.updateUser({
          data: { region: userRegion }
        });

        if (sessionUpdateError) {
          console.error('Error updating session with region:', sessionUpdateError);
        } else {
          console.log('Updated session with region:', userRegion);
        }
      }
    } catch (error) {
      console.error('Error in user login process:', error);
      return {
        success: false,
        message: {"error" :"An error occurred"}
      };
    }
  }

  return {
    success: true
  };
};

export const forgotPasswordAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const supabase = await createClient();
  const origin = (await headers()).get("origin");
  const callbackUrl = formData.get("callbackUrl")?.toString();

  if (!email) {
    return encodedRedirect("error", "/forgot-password", "Email is required");
  }

  // Check if the email exists in the users table
  const { data: existingUser, error: userError } = await supabase
    .from('users')
    .select('email')
    .eq('email', email)
    .single();

  if (userError && userError.code !== 'PGRST116') {
    console.error('Error checking user existence:', userError);
  }

  if (!existingUser) {
    return encodedRedirect(
      "error",
      "/forgot-password",
      "Account with this email not found."
    );
  }

  // If the email exists, proceed with password reset
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${origin}/auth/callback?redirect_to=/reset-password`,
  });

  if (error) {
    console.error(error.message);
    return encodedRedirect(
      "error",
      "/forgot-password",
      "Could not reset password",
    );
  }

  if (callbackUrl) {
    return redirect(callbackUrl);
  }

  return encodedRedirect(
    "success",
    "/forgot-password",
    "Check your email for a link to reset your password.",
  );
};

export const resetPasswordAction = async (formData: FormData) => {
  const supabase = await createClient();

  const password = formData.get("password") as string;
  const confirmPassword = formData.get("confirmPassword") as string;

  if (!password || !confirmPassword) {
    encodedRedirect(
      "error",
      "/reset-password",
      "Password and confirm password are required",
    );
  }

  if (password !== confirmPassword) {
    encodedRedirect(
      "error",
      "/reset-password",
      "Passwords do not match",
    );
  }

  const { error } = await supabase.auth.updateUser({
    password: password,
  });

  if (error) {
    encodedRedirect(
      "error",
      "/reset-password",
      "Password update failed",
    );
  }

  encodedRedirect("success", "/reset-password", "Password updated");
};

export const signOutAction = async () => {
  const supabase = await createClient();
  await supabase.auth.signOut();
  return redirect("/");
};
