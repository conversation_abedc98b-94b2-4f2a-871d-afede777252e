"use client";

import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { UserCircle, CreditCard, Key, Laptop, LogOut, Settings, Users, Shield, List } from "lucide-react";
import UserProfile from "./components/user-profile";
import LicenseSubscription from "./components/subscription-info";
import LicenseInfo from "./components/license-info";
import LicensePayment from "./components/license-payment";
import LicenseDeviceInfo from "./components/license-device-info";
import UserManagementTable from "./components/user-management-table";
import SuperLicense from "./components/super-license";
import LicenseList from "./components/license-list";
import { signOutAction } from "@/app/actions";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { createClient } from "@/utils/supabase/client";

export default function Dashboard() {
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    async function checkAdminRole() {
      try {
        const supabase = createClient();
        const { data: { session } } = await supabase.auth.getSession();

        if (session?.user) {
          // Get user role from session metadata
          const userRole = session.user.user_metadata?.user_role;
          console.log('User role from session:', userRole);

          if (userRole === 'admin') {
            setIsAdmin(true);
          }
        }
      } catch (error) {
        console.error('Error checking admin role:', error);
      }
    }

    checkAdminRole();
  }, []);

  return (
      <div className="container mx-auto px-6 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-4xl font-bold">
            <span style={{ color: 'var(--primary-blue)' }}>Tex</span>Slide Dashboard
          </h1>
          <form action={signOutAction}>
            <Button
              type="submit"
              variant="outline"
              className="flex items-center gap-2 text-lg"
            >
              <LogOut className="h-5 w-5" />
              Logout
            </Button>
          </form>
        </div>
        <div className="flex gap-8">
          <Tabs defaultValue="personal" className="flex-1">
            <div className="flex gap-8">
              <TabsList className="flex flex-col h-fit bg-white p-4 rounded-xl shadow-lg w-56 space-y-2">
                <TabsTrigger
                  value="personal"
                  className="flex items-center gap-3 justify-start data-[state=active]:bg-primary-blue/10 data-[state=active]:text-primary-blue data-[state=active]:shadow-sm w-full px-4 py-3 rounded-lg text-lg font-medium transition-all hover:bg-gray-50 border-b-2 border-transparent"
                >
                  <span>Personal Settings</span>
                </TabsTrigger>
                <TabsTrigger
                  value="subscription0"
                  className="flex items-center gap-3 justify-start data-[state=active]:bg-primary-blue/10 data-[state=active]:text-primary-blue data-[state=active]:shadow-sm w-full px-4 py-3 rounded-lg text-lg font-medium transition-all hover:bg-gray-50 border-b-2 border-transparent"
                >
                  <span>Subscription</span>
                </TabsTrigger>

                {isAdmin && (
                  <TabsTrigger
                    value="admin"
                    className="flex items-center gap-3 justify-start data-[state=active]:bg-primary-blue/10 data-[state=active]:text-primary-blue data-[state=active]:shadow-sm w-full px-4 py-3 rounded-lg text-lg font-medium transition-all hover:bg-gray-50 border-b-2 border-transparent"
                  >
                    <Users className="h-5 w-5 mr-2" />
                    <span>Admin Panel</span>
                  </TabsTrigger>
                )}
              </TabsList>

              <div className="flex-1">
                <TabsContent value="personal">
                  <Card className="p-6 bg-white rounded-lg shadow-xl space-y-8">
                    <UserProfile />
                    <LicenseInfo />
                  </Card>
                </TabsContent>

                <TabsContent value="subscription0">
                  <div className="p-0">
                    <Tabs defaultValue="info" className="w-full">
                      <TabsList className="flex flex-row w-72 bg-transparent p-0 pl-24 mb-6">
                        <TabsTrigger
                          value="info"
                          className="flex-1 px-6 py-3 text-base font-medium data-[state=active]:text-primary-blue data-[state=active]:shadow-sm transition-all"
                        >
                          License Info
                        </TabsTrigger>
                        <TabsTrigger
                          value="subscription"
                          className="flex-1 px-6 py-3 text-base font-medium data-[state=active]:text-primary-blue data-[state=active]:shadow-sm transition-all"
                        >
                          Subscription
                        </TabsTrigger>
                        <TabsTrigger
                          value="invoice"
                          className="flex-1 px-6 py-3 text-base font-medium data-[state=active]:text-primary-blue data-[state=active]:shadow-sm transition-all"
                        >
                          Invoice
                        </TabsTrigger>
                      </TabsList>
                       <TabsContent value="info">
                        <LicenseDeviceInfo />
                      </TabsContent>
                      <TabsContent value="subscription">
                        <LicenseSubscription />
                      </TabsContent>
                      <TabsContent value="invoice">
                        <LicensePayment />
                      </TabsContent>

                    </Tabs>
                  </div>
                </TabsContent>

                {isAdmin && (
                  <TabsContent value="admin">
                    <Card className="p-6 bg-white rounded-lg shadow-xl">
                      <Tabs defaultValue="users" className="w-full">
                        <TabsList className="mb-6">
                          <TabsTrigger value="users" className="flex items-center gap-2">
                            <Users className="h-4 w-4" />
                            User Management
                          </TabsTrigger>
                          <TabsTrigger value="super-license" className="flex items-center gap-2">
                            <Shield className="h-4 w-4" />
                            Super License
                          </TabsTrigger>
                          <TabsTrigger value="license-list" className="flex items-center gap-2">
                            <List className="h-4 w-4" />
                            License List
                          </TabsTrigger>
                        </TabsList>

                        <TabsContent value="users">
                          <UserManagementTable />
                        </TabsContent>

                        <TabsContent value="super-license">
                          <SuperLicense />
                        </TabsContent>

                        <TabsContent value="license-list">
                          <LicenseList />
                        </TabsContent>
                      </Tabs>
                    </Card>
                  </TabsContent>
                )}
              </div>
            </div>
          </Tabs>
        </div>
    </div>
  );
}