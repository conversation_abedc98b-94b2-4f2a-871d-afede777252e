"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Check, Star, Clock } from "lucide-react";
import { useState, useEffect } from "react";
import { createClient } from "@/utils/supabase/client";
import { generateLicenseKey, upgradeUserLicense } from "@/components/license-generator";

const plans = [
  {
    name: "Basic",
    price: "9.99",
    originalPrice: "9.99",
    features: ["Single device", "Maximum 30 formulas per document.", "1 year updates"],
    isFree: true,
  },
  {
    name: "Professional",
    price: "79",
    features: ["2 devices", "Priority support", "Lifetime updates", "Advanced features"],
    popular: true,
  },
];

export default function LicenseSubscription() {
  const [isLoading, setIsLoading] = useState(false);
  const [userRegion, setUserRegion] = useState<string | null>(null);
  const [currencySymbol, setCurrencySymbol] = useState('$');
  const supabase = createClient();

  // Detect user's region when component mounts using only HTML5 Geolocation
  useEffect(() => {
    async function detectUserRegion() {
      try {
        // Default to $ in case geolocation fails
        let region = null;

        // Check if geolocation is available
        if (navigator.geolocation) {
          try {
            const position = await new Promise<GeolocationPosition>((resolve, reject) => {
              navigator.geolocation.getCurrentPosition(resolve, reject, {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 0
              });
            });

            // Use reverse geocoding to get country
            const { latitude, longitude } = position.coords;
            console.log("Geolocation coordinates:", latitude, longitude);

            const response = await fetch(
              `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10`
            );

            if (response.ok) {
              const data = await response.json();
              console.log("Geocoding response:", data);

              if (data.address && data.address.country_code) {
                region = data.address.country_code.toLowerCase();
                console.log("Detected region:", region);
              }
            }
          } catch (error) {
            console.error("Error getting location:", error);
          }
        } else {
          console.log("Geolocation is not supported by this browser");
        }

        // Set currency symbol based on region
        if (region === 'cn' || region === 'china') {
          console.log("Setting currency to Yuan (¥)");
          setCurrencySymbol('¥');
        } else {
          console.log("Setting currency to Dollar ($)");
          setCurrencySymbol('$');
        }

        setUserRegion(region);
      } catch (error) {
        console.error("Error detecting region:", error);
        // Default to $ if region detection fails
        setCurrencySymbol('$');
      }
    }

    detectUserRegion();
  }, []);
  // const licenseGenerator = new LicenseGenerator();

  const handleGetFreePlan = async () => {
    try {
      setIsLoading(true);

      // 获取当前用户
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error("用户未登录");
      }

      // 1. 获取免费支付方式
      const { data: paymentMethods, error: paymentMethodError } = await supabase
        .from('payment_methods')
        .select('id, name, code')
        .eq('code', 'free')
        .eq('is_active', true);

      if (paymentMethodError) {
        console.error('获取支付方式失败:', paymentMethodError);
        throw new Error('获取支付方式失败');
      }

      if (!paymentMethods || paymentMethods.length === 0) {
        console.error('未找到免费支付方式');
        throw new Error('系统配置错误：未找到免费支付方式，请联系管理员');
      }

      const paymentMethod = paymentMethods[0];
      console.log('找到支付方式:', paymentMethod);

      // 2. 生成 license key
      const licenseKey = await generateLicenseKey();

      // 3. 设置 license 的开始和结束时间
      const startDate = new Date();
      const endDate = new Date();
      endDate.setFullYear(endDate.getFullYear() + 1);

      // 4. 插入 license 记录
      const { data: license, error: licenseError } = await supabase
        .from('licenses')
        .insert({
          user_id: user.id,
          license_key: licenseKey,
          status: 'ACTIVE',
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          max_devices: 1,
          features: { formulas_per_document: 30 }
        })
        .select()
        .single();

      if (licenseError) {
        console.error('创建许可证失败:', licenseError);
        throw new Error('创建许可证失败');
      }

      console.log('许可证创建成功:', license);

      // 5. 插入 payment_transaction 记录
      const { data: transaction, error: transactionError } = await supabase
        .from('payment_transactions')
        .insert({
          payment_method_id: paymentMethod.id,  // 关联 payment_methods 表
          user_id: user.id,  // 添加用户ID
          amount: 0,
          currency: userRegion === 'cn' || userRegion === 'china' ? 'CNY' : 'USD',
          status: 'completed',
          payment_data: {
            type: 'free_plan',
            payment_method_id: paymentMethod.id,
            payment_method_name: paymentMethod.name
          },
          completed_at: new Date().toISOString()
        })
        .select()
        .single();

      if (transactionError) {
        console.error('创建支付记录失败:', transactionError);
        await supabase.from('licenses').delete().eq('id', license.id);
        throw new Error('创建支付记录失败');
      }

      console.log('支付记录创建成功:', transaction);

      // 6. 插入 license_payment 记录，关联 licenses 和 payment_transactions 表
      const { error: paymentError } = await supabase
        .from('license_payments')
        .insert({
          license_id: license.id,  // 关联 licenses 表
          payment_transaction_id: transaction.id,  // 关联 payment_transactions 表
          amount: 0,
          currency: userRegion === 'cn' || userRegion === 'china' ? 'CNY' : 'USD',
          payment_date: new Date().toISOString(),
          status: 'completed'
        });

      if (paymentError) {
        console.error('创建许可证支付记录失败:', paymentError);
        // 如果失败，需要回滚之前创建的所有记录
        await Promise.all([
          supabase.from('licenses').delete().eq('id', license.id),
          supabase.from('payment_transactions').delete().eq('id', transaction.id)
        ]);
        throw new Error('创建许可证支付记录失败');
      }

      console.log('许可证支付记录创建成功');
      alert("免费计划已成功激活！");
    } catch (error) {
      console.error("激活免费计划时出错:", error);
      alert(error instanceof Error ? error.message : "激活免费计划失败，请重试");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Choose Your Subscription</h2>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {plans.map((plan) => (
          <Card
            key={plan.name}
            className={`p-6 flex flex-col h-full ${plan.popular ? 'border-primary' : ''}`}
          >
            {plan.popular && (
              <div className="absolute top-0 right-0 -translate-y-1/2 translate-x-1/2">
                <span className="inline-flex items-center gap-1 rounded-full bg-primary px-2 py-1 text-xs text-primary-foreground">
                  <Star className="h-3 w-3 fill-current" />
                  Popular
                </span>
              </div>
            )}
            {plan.isFree && (
              <div className="absolute top-0 right-0 -translate-y-1/2 translate-x-1/2">
                <span className="inline-flex items-center gap-1 rounded-full bg-green-500 px-2 py-1 text-xs text-white">
                  <Clock className="h-3 w-3" />
                  Limited Time Free
                </span>
              </div>
            )}
            <div className="flex flex-col flex-1">
              <div className="space-y-4">
                <h3 className="text-xl font-bold">{plan.name}</h3>
                <div className="text-3xl font-bold">
                  {plan.isFree ? (
                    <>
                      <span className="text-green-500">Free</span>
                      <span className="text-sm font-normal ml-2">
                        <span className="line-through">{currencySymbol}{plan.originalPrice}</span>
                        <span>/year</span>
                      </span>
                    </>
                  ) : (
                    <>
                      {currencySymbol}{plan.price}
                      <span className="text-sm font-normal text-muted-foreground">/year</span>
                    </>
                  )}
                </div>
                <ul className="space-y-2">
                  {plan.features.map((feature) => (
                    <li key={feature} className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-primary" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
              <div className="mt-auto pt-6">
                <Button
                  className="w-full"
                  variant={plan.popular ? "default" : plan.isFree ? "default" : "outline"}
                  onClick={plan.isFree ? handleGetFreePlan : undefined}
                  disabled={isLoading}
                >
                  {isLoading ? "Processing..." : (plan.isFree ? "Get Free Plan" : `Choose ${plan.name}`)}
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}