import { createClient } from '@/utils/supabase/client'

export async function signInWithGithub(redirectTo: string = '/dashboard', signup: boolean = false) {
  const supabase = createClient();
  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'github',
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback?redirect_to=${redirectTo}&signup=${signup}&github=true`,
      }
    });
    
    if (error) {
      throw error;
    }
    
    return { data, error: null };
  } catch (error) {
    return { 
      data: null, 
      error: error instanceof Error ? error.message : 'An error occurred during GitHub authentication' 
    };
  }
}