"use client";

/**
 * Detects the user's region using HTML5 Geolocation and reverse geocoding
 * @returns Promise<string | null> - Returns the country code (lowercase) or null if detection fails
 */
export async function detectUserRegion(): Promise<string | null> {
  try {
    // Default to null in case geolocation fails
    let region = null;

    // Check if geolocation is available
    if (typeof navigator !== 'undefined' && navigator.geolocation) {
      try {
        const position = await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 0
          });
        });

        // Use reverse geocoding to get country
        const { latitude, longitude } = position.coords;
        console.log("Geolocation coordinates:", latitude, longitude);

        const response = await fetch(
          `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10`
        );

        if (response.ok) {
          const data = await response.json();
          console.log("Geocoding response:", data);

          if (data.address && data.address.country_code) {
            region = data.address.country_code.toLowerCase();
            console.log("Detected region:", region);
          }
        }
      } catch (error) {
        console.error("Error getting location:", error);
      }
    } else {
      console.log("Geolocation is not supported by this browser");
    }

    return region;
  } catch (error) {
    console.error("Error detecting region:", error);
    return null;
  }
}

/**
 * Gets the appropriate currency symbol based on the region
 * @param region - The region code (e.g., 'cn', 'us')
 * @returns The currency symbol ('¥' for China, '$' for others)
 */
export function getCurrencySymbol(region: string | null): string {
  if (region === 'cn' || region === 'china') {
    return '¥';
  }
  return '$';
}

/**
 * Gets the currency code based on the region
 * @param region - The region code (e.g., 'cn', 'us')
 * @returns The currency code ('CNY' for China, 'USD' for others)
 */
export function getCurrencyCode(region: string | null): string {
  if (region === 'cn' || region === 'china') {
    return 'CNY';
  }
  return 'USD';
}
