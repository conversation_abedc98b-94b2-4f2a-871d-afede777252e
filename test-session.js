// 临时测试文件 - 用于验证 session 中的 user_role
// 在浏览器控制台中运行此代码来检查 session

console.log('Testing session user_role...');

// 获取当前 session
async function testSession() {
  try {
    const response = await fetch('/api/auth/session');
    const session = await response.json();
    
    console.log('Current session:', session);
    console.log('User metadata:', session?.user?.user_metadata);
    console.log('User role:', session?.user?.user_metadata?.user_role);
    
    if (session?.user?.user_metadata?.user_role) {
      console.log('✅ User role found in session:', session.user.user_metadata.user_role);
    } else {
      console.log('❌ User role not found in session');
    }
  } catch (error) {
    console.error('Error testing session:', error);
  }
}

testSession();
