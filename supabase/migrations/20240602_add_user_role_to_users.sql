-- Add user_role column to users table
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS user_role TEXT DEFAULT 'user';

-- Create index for user_role column
CREATE INDEX IF NOT EXISTS idx_users_user_role ON public.users(user_role);

-- Add constraint to ensure valid user roles
ALTER TABLE public.users DROP CONSTRAINT IF EXISTS check_user_role;
ALTER TABLE public.users ADD CONSTRAINT check_user_role 
CHECK (user_role IN ('user', 'admin', 'moderator'));

-- Update existing users to have default role if null
UPDATE public.users SET user_role = 'user' WHERE user_role IS NULL;
