<svg width="800" height="450" viewBox="0 0 800 450" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="450" fill="#f8fafc"/>
  
  <!-- Grid pattern -->
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#e2e8f0" stroke-width="1"/>
    </pattern>
  </defs>
  <rect width="800" height="450" fill="url(#grid)"/>
  
  <!-- Main content area -->
  <rect x="50" y="50" width="700" height="350" fill="white" stroke="#cbd5e1" stroke-width="2" rx="8"/>
  
  <!-- TexSlide logo area -->
  <rect x="80" y="80" width="200" height="60" fill="#3b82f6" rx="4"/>
  <text x="180" y="115" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">TexSlide</text>
  
  <!-- Formula demonstration area -->
  <rect x="320" y="80" width="400" height="120" fill="#f1f5f9" stroke="#cbd5e1" stroke-width="1" rx="4"/>
  <text x="520" y="130" text-anchor="middle" fill="#475569" font-family="serif" font-size="18">∫ f(x)dx = F(x) + C</text>
  <text x="520" y="155" text-anchor="middle" fill="#64748b" font-family="Arial, sans-serif" font-size="14">LaTeX Formula Integration</text>
  
  <!-- Feature highlights -->
  <circle cx="120" cy="250" r="20" fill="#10b981"/>
  <text x="160" y="255" fill="#374151" font-family="Arial, sans-serif" font-size="14">LibreOffice Integration</text>
  
  <circle cx="120" cy="290" r="20" fill="#f59e0b"/>
  <text x="160" y="295" fill="#374151" font-family="Arial, sans-serif" font-size="14">Full LaTeX Support</text>
  
  <circle cx="120" cy="330" r="20" fill="#ef4444"/>
  <text x="160" y="335" fill="#374151" font-family="Arial, sans-serif" font-size="14">Professional Output</text>
  
  <!-- Play button overlay -->
  <circle cx="400" cy="225" r="40" fill="rgba(59, 130, 246, 0.9)"/>
  <polygon points="385,210 385,240 415,225" fill="white"/>
  
  <!-- Video title -->
  <text x="400" y="420" text-anchor="middle" fill="#1f2937" font-family="Arial, sans-serif" font-size="16" font-weight="bold">TexSlide Product Demonstration</text>
</svg>
